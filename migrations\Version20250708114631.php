<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250708114631 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE heures_detail (id INT AUTO_INCREMENT NOT NULL, heures_entry_id INT NOT NULL, day DATE NOT NULL, type VARCHAR(20) NOT NULL, category VARCHAR(2) NOT NULL, hours NUMERIC(4, 2) DEFAULT NULL, INDEX IDX_E8CEEA4B73B2A756 (heures_entry_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE heures_entry (id INT AUTO_INCREMENT NOT NULL, osi_id INT NOT NULL, ouvrier_id INT NOT NULL, start_week_date DATE NOT NULL, INDEX IDX_EF6DF74A293C2298 (osi_id), INDEX IDX_EF6DF74A4E853A9E (ouvrier_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_detail ADD CONSTRAINT FK_E8CEEA4B73B2A756 FOREIGN KEY (heures_entry_id) REFERENCES heures_entry (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_entry ADD CONSTRAINT FK_EF6DF74A293C2298 FOREIGN KEY (osi_id) REFERENCES osi (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_entry ADD CONSTRAINT FK_EF6DF74A4E853A9E FOREIGN KEY (ouvrier_id) REFERENCES info_ouvrier (id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE heures
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi CHANGE nom_client nom_client VARCHAR(255) NOT NULL, CHANGE nom_projet nom_projet VARCHAR(255) NOT NULL, CHANGE numero_osi numero_osi INT NOT NULL, CHANGE numero_achat numero_achat DOUBLE PRECISION DEFAULT NULL, CHANGE date_debut date_debut DATE DEFAULT NULL, CHANGE date_fin date_fin DATE DEFAULT NULL, CHANGE billable billable TINYINT(1) NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE heures (id INT AUTO_INCREMENT NOT NULL, voyage_n1 DOUBLE PRECISION NOT NULL, voyage_n2 DOUBLE PRECISION NOT NULL, stand_by_n1 DOUBLE PRECISION NOT NULL, stand_by_n2 DOUBLE PRECISION NOT NULL, working_n1 DOUBLE PRECISION NOT NULL, working_n2 DOUBLE PRECISION NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = '' 
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_detail DROP FOREIGN KEY FK_E8CEEA4B73B2A756
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_entry DROP FOREIGN KEY FK_EF6DF74A293C2298
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_entry DROP FOREIGN KEY FK_EF6DF74A4E853A9E
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE heures_detail
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE heures_entry
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi CHANGE nom_client nom_client VARCHAR(255) DEFAULT NULL, CHANGE nom_projet nom_projet VARCHAR(255) DEFAULT NULL, CHANGE numero_osi numero_osi INT UNSIGNED NOT NULL, CHANGE numero_achat numero_achat DOUBLE PRECISION UNSIGNED DEFAULT NULL, CHANGE date_debut date_debut DATE NOT NULL, CHANGE date_fin date_fin DATE NOT NULL, CHANGE billable billable TINYINT(1) DEFAULT NULL
        SQL);
    }
}
