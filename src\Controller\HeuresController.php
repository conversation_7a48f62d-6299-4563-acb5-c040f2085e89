<?php

namespace App\Controller;

use App\Entity\OSI;
use App\Entity\InfoOuvrier;
use App\Entity\HeuresEntry;
use App\Entity\HeuresDetail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;

class HeuresController extends AbstractController
{
    #[Route('/heures/{nomprenom}/{numeroOSI}/{date}', name: 'app_heures_detail')]
    public function index(string $nomprenom, int $numeroOSI, string $date, EntityManagerInterface $em, Request $request): Response
    {
        $osi = $em->getRepository(OSI::class)->findOneBy(['NumeroOSI' => $numeroOSI]);

        if (!$osi) {
            throw $this->createNotFoundException('OSI not found');
        }

        // Find the matching ouvrier
        $ouvrier = null;
        foreach ($osi->getListeOuvriers() as $o) {
            $generated = strtolower(substr($o->getPrenom(), 0, 1) . $o->getNom());
            if ($generated === strtolower($nomprenom)) {
                $ouvrier = $o;
                break;
            }
        }

        if (!$ouvrier) {
            throw $this->createNotFoundException('Ouvrier not found in this OSI');
        }

        // Parse date from URL (expected format: ddmmyyyy)
        $weekStart = \DateTime::createFromFormat('dmY', $date);
        if (!$weekStart) {
            throw $this->createNotFoundException('Invalid date format');
        }

        // Get Monday of that week
        $dayOfWeek = (int) $weekStart->format('N'); // 1 = Monday, 7 = Sunday
        $startMonday = (clone $weekStart)->modify('-' . ($dayOfWeek - 1) . ' days');

        // Prepare normalized dates for comparison
        $startDateStr = $osi->getDateDebut()->format('Y-m-d');
        $endDateStr = $osi->getDateFin()->format('Y-m-d');

        // Build array for 7 days with proper date range check ignoring time
        $days = [];
        for ($i = 0; $i < 7; $i++) {
            $currentDay = (clone $startMonday)->modify("+{$i} days");
            $currentDateStr = $currentDay->format('Y-m-d');

            $inRange = ($currentDateStr >= $startDateStr) && ($currentDateStr <= $endDateStr);

            $days[] = [
                'label' => $currentDay->format('l'), // Full weekday name
                'date' => $currentDay->format('d/m'),
                'full' => $currentDay,
                'active' => $inRange,
            ];
        }

        // Load existing HeuresEntry for this osi, ouvrier, and week
        $heuresEntryRepo = $em->getRepository(HeuresEntry::class);
        $heuresEntry = $heuresEntryRepo->findOneBy([
            'osi' => $osi,
            'ouvrier' => $ouvrier,
            'startWeekDate' => $startMonday,
        ]);

        if ($request->isMethod('POST')) {
            // Fixed: don't pass array as second arg to get()
            $formData = $request->request->all()['hours'] ?? [];

            if (!$heuresEntry) {
                $heuresEntry = new HeuresEntry();
                $heuresEntry->setOsi($osi);
                $heuresEntry->setOuvrier($ouvrier);
                $heuresEntry->setStartWeekDate($startMonday);
            }

            // Remove existing details to avoid duplicates
            foreach ($heuresEntry->getDetails() as $oldDetail) {
                $heuresEntry->removeDetail($oldDetail);
                $em->remove($oldDetail);
            }

            // Add new details from form data
            foreach ($formData as $type => $categories) {
                foreach ($categories as $category => $dates) {
                    foreach ($dates as $dayStr => $hours) {
                        $hoursFloat = floatval($hours);
                        if ($hoursFloat > 0) {
                            $detail = new HeuresDetail();
                            $detail->setHeuresEntry($heuresEntry);
                            $detail->setType($type);
                            $detail->setCategory($category);
                            $detail->setHours($hoursFloat);

                            $dayDate = \DateTime::createFromFormat('Y-m-d', $dayStr);
                            $detail->setDay($dayDate);

                            $heuresEntry->addDetail($detail);
                            $em->persist($detail);
                        }
                    }
                }
            }

            $em->persist($heuresEntry);
            $em->flush();

            // Redirect after POST to avoid resubmission
            return $this->redirectToRoute('app_heures_detail', [
                'nomprenom' => $nomprenom,
                'numeroOSI' => $numeroOSI,
                'date' => $date,
            ]);
        }

        // Prepare existing hours array for pre-filling the form inputs
        $existingHours = [];
        if ($heuresEntry) {
            foreach ($heuresEntry->getDetails() as $detail) {
                $type = $detail->getType();
                $category = $detail->getCategory();
                $dayStr = $detail->getDay()->format('Y-m-d');
                $existingHours[$type][$category][$dayStr] = $detail->getHours();
            }
        }

        return $this->render('heures/detail.html.twig', [
            'ouvrier' => $ouvrier,
            'osi' => $osi,
            'week_start' => $startMonday,
            'days' => $days,
            'existing_hours' => $existingHours,
        ]);
    }
}
