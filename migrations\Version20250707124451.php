<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250707124451 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE heures_day (id INT AUTO_INCREMENT NOT NULL, heures_id INT NOT NULL, date DATETIME NOT NULL, working_n1 DOUBLE PRECISION NOT NULL, standby_n1 DOUBLE PRECISION NOT NULL, travel_n1 DOUBLE PRECISION NOT NULL, working_n2 DOUBLE PRECISION NOT NULL, standby_n2 DOUBLE PRECISION NOT NULL, travel_n2 DOUBLE PRECISION NOT NULL, INDEX IDX_C7CD5D6FD05B21E6 (heures_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_day ADD CONSTRAINT FK_C7CD5D6FD05B21E6 FOREIGN KEY (heures_id) REFERENCES heures (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD ouvrier_id INT DEFAULT NULL, ADD osi_id INT NOT NULL, ADD week_start DATETIME NOT NULL, DROP voyage_n1, DROP voyage_n2, DROP stand_by_n1, DROP stand_by_n2, DROP working_n1, DROP working_n2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD CONSTRAINT FK_DEA5875D4E853A9E FOREIGN KEY (ouvrier_id) REFERENCES info_ouvrier (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD CONSTRAINT FK_DEA5875D293C2298293C2298 FOREIGN KEY (osi_id) REFERENCES osi (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DEA5875D4E853A9E ON heures (ouvrier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DEA5875D293C2298 ON heures (osi_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi CHANGE nom_client nom_client VARCHAR(255) NOT NULL, CHANGE nom_projet nom_projet VARCHAR(255) NOT NULL, CHANGE pays pays VARCHAR(255) NOT NULL, CHANGE ville ville VARCHAR(255) NOT NULL, CHANGE site site VARCHAR(255) NOT NULL, CHANGE numero_osi numero_osi INT NOT NULL, CHANGE numero_achat numero_achat DOUBLE PRECISION NOT NULL, CHANGE billable billable TINYINT(1) NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE heures_day DROP FOREIGN KEY FK_C7CD5D6FD05B21E6
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE heures_day
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures DROP FOREIGN KEY FK_DEA5875D4E853A9E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures DROP FOREIGN KEY FK_DEA5875D293C2298293C2298
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_DEA5875D4E853A9E ON heures
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_DEA5875D293C2298 ON heures
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD voyage_n1 DOUBLE PRECISION NOT NULL, ADD voyage_n2 DOUBLE PRECISION NOT NULL, ADD stand_by_n1 DOUBLE PRECISION NOT NULL, ADD stand_by_n2 DOUBLE PRECISION NOT NULL, ADD working_n1 DOUBLE PRECISION NOT NULL, ADD working_n2 DOUBLE PRECISION NOT NULL, DROP ouvrier_id, DROP osi_id, DROP week_start
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi CHANGE nom_client nom_client VARCHAR(255) DEFAULT NULL, CHANGE nom_projet nom_projet VARCHAR(255) DEFAULT NULL, CHANGE pays pays VARCHAR(255) DEFAULT NULL, CHANGE ville ville VARCHAR(255) DEFAULT NULL, CHANGE site site VARCHAR(255) DEFAULT NULL, CHANGE numero_osi numero_osi INT UNSIGNED NOT NULL, CHANGE numero_achat numero_achat DOUBLE PRECISION UNSIGNED DEFAULT NULL, CHANGE billable billable TINYINT(1) DEFAULT NULL
        SQL);
    }
}
