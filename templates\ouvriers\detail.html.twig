{% extends 'base.html.twig' %}

{% block title %}Ouvrier {{ ouvrier.nom }} {{ ouvrier.prenom }} - OSI {{ osi.NumeroOSI }}{% endblock %}

{% block body %}
  <h1>Ouvrier : {{ ouvrier.nom }} {{ ouvrier.prenom }}</h1>
  <p>OSI N°{{ osi.NumeroOSI }} du {{ osi.DateDebut|date('d/m/Y') }} au {{ osi.DateFin|date('d/m/Y') }}</p>

  <table border="1" cellspacing="0" cellpadding="5">
    <thead>
      <tr>
        <th>Semaine</th>
        <th>Heures Travaillées / Voyagées</th>
        <th>Heures Stand-By</th>
        <th>Total Primes</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      {% for week in weeks %}
        <tr>
          <td>{{ week.start|date('d/m/Y') }} au {{ week.end|date('d/m/Y') }}</td>
          <td>{{ weeklyHours[week.start|date('Y-m-d')] is defined ? weeklyHours[week.start|date('Y-m-d')] : 0 }}</td>
          <td>{{ weeklyStandby[week.start|date('Y-m-d')] is defined ? weeklyStandby[week.start|date('Y-m-d')] : 0 }}</td>
          <td>{{ weeklyPrimes[week.start|date('Y-m-d')] is defined ? weeklyPrimes[week.start|date('Y-m-d')]|number_format(2, ',', ' ') : '0,00' }}</td>
          <td>
            <a href="{{ path('app_heures_detail', {
              'nomprenom': (ouvrier.prenom|lower|slice(0,1)) ~ (ouvrier.nom|lower),
              'numeroOSI': osi.NumeroOSI,
              'date': week.start|date('dmY')
            }) }}">Configurer</a>
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>

  <form method="post" action="{{ path('app_ouvrier_detail', {'prenomnom': (ouvrier.prenom|lower|slice(0,1)) ~ (ouvrier.nom|lower), 'numeroOSI': osi.NumeroOSI}) }}">
    <label for="contratHoraire">Contrat Horaire (heures par semaine) :</label>
    <input type="number" min="0" max="100" step="0.25" id="contratHoraire" name="contratHoraire" value="{{ contratHoraire ?? '' }}" required>
    <button type="submit">Sauvegarder</button>
    <div id="contratHoraireError" style="color: red; font-size: 0.9em; margin-top: 4px; visibility: hidden; height: 1.2em;">
      Veuillez remplir le champ Contrat Horaire avant d'approuver les heures.
    </div>
  </form>

  <p><a href="{{ path('app_osi_detail', {'numeroOSI': osi.NumeroOSI}) }}">← Retour aux détails OSI</a></p>

  <p>
    <a href="{{ path('app_ouvrier_approuver', {
      'prenomnom': (ouvrier.prenom|lower|slice(0,1)) ~ ouvrier.nom|lower,
      'numeroOSI': osi.NumeroOSI
    }) }}"
    id="approveHoursLink"
    onclick="
      const contratHoraireInput = document.getElementById('contratHoraire');
      const errorBox = document.getElementById('contratHoraireError');
      if (!contratHoraireInput.value || contratHoraireInput.value <= 0) {
        errorBox.style.visibility = 'visible';
        contratHoraireInput.focus();
        return false;
      } else {
        errorBox.style.visibility = 'hidden';
      }
    "
    >Approuver les heures →</a>
  </p>
{% endblock %}
