{% extends 'base.html.twig' %}

{% block title %}Heures - {{ ouvrier.nom }} {{ ouvrier.prenom }}{% endblock %}

{% block body %}
  <h1>Configurer les heures</h1>
  <h3>(en centièmes)</h3>
  <p>Ouvrier : {{ ouvrier.nom }} {{ ouvrier.prenom }}</p>
  <p>OSI N°{{ osi.NumeroOSI }} — <PERSON><PERSON><PERSON> {{ week_start|date('d/m/Y') }}</p>

  <form method="post" action="#">
    <table border="1" cellspacing="0" cellpadding="5">
      <thead>
        {% set frenchDays = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'] %}
        <tr>
          <th>   </th>
          {% for i in 0..6 %}
            <th>{{ frenchDays[i] }}</th>
          {% endfor %}
        </tr>
        <tr>
          <th>Date</th>
          {% for day in days %}
            <th>{{ day.date }}</th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% set types = ['Voyage', 'Travail', 'Stand-By'] %}
        {% set niveaux = ['N1', 'N2'] %}

        {% for type in types %}
          {% for niveau in niveaux %}
            <tr>
              <td>{{ type }} {{ niveau }}</td>
              {% for day in days %}
                <td>
                  {% if day.active %}
                    <input type="number"
                           name="hours[{{ type }}][{{ niveau }}][{{ day.full|date('Y-m-d') }}]"
                           min="0" max="24" step="0.25"
                           style="width: 60px;"
                           value="{{ existing_hours[type][niveau][day.full|date('Y-m-d')] | default('') }}">
                  {% else %}
                    <input type="number" disabled value="" style="background-color: #ccc; width: 60px;">
                  {% endif %}
                </td>
              {% endfor %}
            </tr>
          {% endfor %}
        {% endfor %}
      </tbody>
    </table>

    <button type="submit">Enregistrer</button>
  </form>

  <p><a href="{{ path('app_ouvrier_detail', {
    'prenomnom': (ouvrier.prenom|lower|slice(0,1)) ~ ouvrier.nom|lower,
    'numeroOSI': osi.NumeroOSI
  }) }}">← Retour à l’ouvrier</a></p>
{% endblock %}
