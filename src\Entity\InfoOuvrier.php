<?php

namespace App\Entity;

use App\Repository\InfoOuvrierRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: InfoOuvrierRepository::class)]
class InfoOuvrier
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $nom = null;

    #[ORM\Column(length: 255)]
    private ?string $prenom = null;

    #[ORM\OneToMany(mappedBy: "ouvrier", targetEntity: OuvrierOSIContrat::class, cascade: ["persist", "remove"])]
    private Collection $osiContrats;

    public function __construct()
    {
        $this->osiContrats = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;
        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(string $prenom): static
    {
        $this->prenom = $prenom;
        return $this;
    }

    /**
     * @return Collection|OuvrierOSIContrat[]
     */
    public function getOsiContrats(): Collection
    {
        return $this->osiContrats;
    }

    public function addOsiContrat(OuvrierOSIContrat $contrat): static
    {
        if (!$this->osiContrats->contains($contrat)) {
            $this->osiContrats[] = $contrat;
            $contrat->setOuvrier($this);
        }
        return $this;
    }

    public function removeOsiContrat(OuvrierOSIContrat $contrat): static
    {
        if ($this->osiContrats->removeElement($contrat)) {
            if ($contrat->getOuvrier() === $this) {
                $contrat->setOuvrier(null);
            }
        }
        return $this;
    }
}
