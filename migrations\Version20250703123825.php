<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250703123825 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE osi_info_ouvrier (osi_id INT NOT NULL, info_ouvrier_id INT NOT NULL, INDEX IDX_540E11C4293C2298 (osi_id), INDEX IDX_540E11C416400632 (info_ouvrier_id), PRIMARY KEY(osi_id, info_ouvrier_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi_info_ouvrier ADD CONSTRAINT FK_540E11C4293C2298 FOREIGN KEY (osi_id) REFERENCES osi (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi_info_ouvrier ADD CONSTRAINT FK_540E11C416400632 FOREIGN KEY (info_ouvrier_id) REFERENCES info_ouvrier (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD liste_ouvriers_id INT DEFAULT NULL, DROP ouvrier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD CONSTRAINT FK_DEA5875DE9F65AB0 FOREIGN KEY (liste_ouvriers_id) REFERENCES info_ouvrier (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DEA5875DE9F65AB0 ON heures (liste_ouvriers_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi DROP liste_ouvriers
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE osi_info_ouvrier DROP FOREIGN KEY FK_540E11C4293C2298
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi_info_ouvrier DROP FOREIGN KEY FK_540E11C416400632
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE osi_info_ouvrier
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures DROP FOREIGN KEY FK_DEA5875DE9F65AB0
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_DEA5875DE9F65AB0 ON heures
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD ouvrier VARCHAR(255) NOT NULL, DROP liste_ouvriers_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi ADD liste_ouvriers VARCHAR(255) NOT NULL
        SQL);
    }
}
