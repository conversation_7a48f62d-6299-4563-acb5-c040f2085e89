<?php

namespace App\Repository;

use App\Entity\OuvrierOSIContrat;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class OuvrierOSIContratRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OuvrierOSIContrat::class);
    }

    // Vous pouvez ajouter ici des méthodes personnalisées de recherche si nécessaire
}
