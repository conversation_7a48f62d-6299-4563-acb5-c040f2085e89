<?php

namespace App\Form;

use App\Entity\OSI;
use App\Entity\InfoOuvrier;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OSIForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('NomClient', TextType::class, [
                'label' => 'Nom du Client ',
            ])
            ->add('NomProjet', TextType::class, [
                'label' => 'Nom du Projet ',
                'required' => false,
            ])
            ->add('Pays', TextType::class, [
                'label' => 'Pays ',
            ])
            ->add('Ville', TextType::class, [
                'label' => 'Ville ',
            ])
            ->add('Site', TextType::class, [
                'label' => 'Site ',
                'required' => false,
            ])
            ->add('NumeroOSI', IntegerType::class, [
                'label' => 'Numero OSI ',
                'attr' => ['min' => 0, 'step' => 1],
            ])
            ->add('NumeroAchat', TextType::class, [
                'label' => 'Numero d\'achat ',
                'required' => false,
            ])
            ->add('DateDebut', DateType::class, [
                'label' => 'Date de Début ',
            ])
            ->add('DateFin', DateType::class, [
                'label' => 'Date de Fin ',
            ])
            ->add('ListeOuvriers', EntityType::class, [
                'class' => InfoOuvrier::class,
                'choice_label' => function ($ouvrier) {
                    return $ouvrier->getNom() . ' ' . $ouvrier->getPrenom();
                },
                'query_builder' => function (\App\Repository\InfoOuvrierRepository $repo) {
                    return $repo->createQueryBuilder('w')
                                ->orderBy('w.nom', 'ASC');
                },
                'multiple' => true,
                'expanded' => false,
                'label' => 'Liste des ouvriers ',
                'attr' => ['size' => 7,],
            ])
            ->add('Billable', CheckboxType::class, [
                'label' => 'Facturable ? ',
                'required' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => OSI::class,
        ]);
    }
}
