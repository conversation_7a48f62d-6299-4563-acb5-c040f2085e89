<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class HeuresDetail
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type:"integer")]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: HeuresEntry::class, inversedBy: "details")]
    #[ORM\JoinColumn(nullable: false)]
    private ?HeuresEntry $heuresEntry = null;

    #[ORM\Column(type:"date")]
    private ?\DateTimeInterface $day = null;

    #[ORM\Column(type:"string", length: 20)]
    private ?string $type = null; // 'Voyage', 'Travail', 'Stand-By'

    #[ORM\Column(type:"string", length: 2)]
    private ?string $category = null; // 'N1' or 'N2'

    #[ORM\Column(type:"decimal", precision: 4, scale: 2, nullable: true)]
    private ?float $hours = null;

    // getters/setters...

    public function getId(): ?int { return $this->id; }

    public function getHeuresEntry(): ?HeuresEntry { return $this->heuresEntry; }
    public function setHeuresEntry(?HeuresEntry $entry): static { $this->heuresEntry = $entry; return $this; }

    public function getDay(): ?\DateTimeInterface { return $this->day; }
    public function setDay(\DateTimeInterface $day): static { $this->day = $day; return $this; }

    public function getType(): ?string { return $this->type; }
    public function setType(string $type): static { $this->type = $type; return $this; }

    public function getCategory(): ?string { return $this->category; }
    public function setCategory(string $category): static { $this->category = $category; return $this; }

    public function getHours(): ?float { return $this->hours; }
    public function setHours(?float $hours): static { $this->hours = $hours; return $this; }
}
