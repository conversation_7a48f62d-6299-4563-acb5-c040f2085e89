<?php

namespace App\Controller;

use App\Entity\InfoOuvrier;
use App\Entity\HeuresEntry;
use App\Entity\HeuresDetail;
use App\Entity\OuvrierOSIContrat;
use App\Entity\OSI;
use App\Entity\Ouvrier;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request; 
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Dompdf\Dompdf;
use Dompdf\Options;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;


class OuvrierController extends AbstractController
{
    #[Route('/ouvrier/{prenomnom}/{numeroOSI}', name: 'app_ouvrier_detail')]
public function detail(string $prenomnom, int $numeroOSI, Request $request, EntityManagerInterface $em): Response
{
    // Find the OSI entity by NumeroOSI
    $osi = $em->getRepository(OSI::class)->findOneBy(['NumeroOSI' => $numeroOSI]);

    if (!$osi) {
        throw $this->createNotFoundException('OSI not found');
    }

    // Find the Ouvrier matching the prenomnom slug
    $ouvrier = null;
    foreach ($osi->getListeOuvriers() as $o) {
        $generated = strtolower(substr($o->getPrenom(), 0, 1) . $o->getNom());
        if ($generated === strtolower($prenomnom)) {
            $ouvrier = $o;
            break;
        }
    }

    if (!$ouvrier) {
        throw $this->createNotFoundException('Ouvrier not found in this OSI');
    }

    // Get or create the OuvrierOSIContrat for this ouvrier and osi
    $contract = null;
    foreach ($ouvrier->getOsiContrats() as $contrat) {
        if ($contrat->getOsi() && $contrat->getOsi()->getId() === $osi->getId()) {
            $contract = $contrat;
            break;
        }
    }

    if (!$contract) {
        $contract = new OuvrierOSIContrat();
        $contract->setOsi($osi);
        $contract->setOuvrier($ouvrier);
        $em->persist($contract);
    }

    // Handle POST form submission to update contratHoraire
    if ($request->isMethod('POST')) {
        $contratHoraire = $request->request->get('contratHoraire');
        if ($contratHoraire !== null) {
            // Convert comma to dot for decimals and cast to float
            $contratHoraireFloat = floatval(str_replace(',', '.', $contratHoraire));
            $contract->setContratHoraire($contratHoraireFloat);
            $em->flush();

            $this->addFlash('success', 'Contrat horaire mis à jour.');

            // Redirect to avoid form resubmission on refresh
            return $this->redirectToRoute('app_ouvrier_detail', [
                'prenomnom' => $prenomnom,
                'numeroOSI' => $numeroOSI,
            ]);
        }
    }

    // Align start date to Monday
    $start = clone $osi->getDateDebut();
    $dayOfWeek = (int)$start->format('N'); // 1=Monday
    if ($dayOfWeek !== 1) {
        $start->modify('-' . ($dayOfWeek - 1) . ' days');
    }

    // Adjust end date to next Sunday
    $end = clone $osi->getDateFin();
    $endDayOfWeek = (int)$end->format('N'); // 7=Sunday
    if ($endDayOfWeek !== 7) {
        $end->modify('next sunday');
    }

    // Compute full weeks
    $weeks = [];
    $tempStart = clone $start;
    while ($tempStart <= $end) {
        $weekStart = clone $tempStart;
        $weekEnd = (clone $tempStart)->modify('+6 days');

        $weeks[] = [
            'start' => $weekStart,
            'end' => $weekEnd,
        ];

        $tempStart->modify('+7 days');
    }

    // Determine zone
    $country = $osi->getPays();
    $eurozoneCountries = [
        'France', 'Germany', 'Allemagne', 'Italy', 'Italie', 'Spain', 'Espagne', 'Portugal',
        'Belgique', 'Belgium', 'Netherlands', 'Pays-Bas', 'Austria', 'Autriche',
        'Finland', 'Finlande', 'Ireland', 'Irlande', 'Greece', 'Grèce',
        'Slovakia', 'Slovaquie', 'Slovenia', 'Slovénie', 'Estonia', 'Estonie',
        'Latvia', 'Lettonie', 'Lithuania', 'Lituanie', 'Luxembourg', 'Malta', 'Malte', 'Cyprus', 'Chypre',
    ];

    $zone = in_array(ucfirst(strtolower($country)), $eurozoneCountries) ? 'Zone Euros' : 'Hors Zone Euros';

    $rates = [
        'Zone Euros' => [
            'JourSemaine' => ['N1' => 130, 'N2' => 230],
            'JourWEFerieTravail' => ['N1' => 160, 'N2' => 335],
            'JourWENonTravaille' => ['N1' => 90, 'N2' => 180],
            'JourWEFerieVoyage' => ['N1' => 160, 'N2' => 160],
        ],
        'Hors Zone Euros' => [
            'JourSemaine' => ['N1' => 150, 'N2' => 250],
            'JourWEFerieTravail' => ['N1' => 180, 'N2' => 355],
            'JourWENonTravaille' => ['N1' => 110, 'N2' => 200],
            'JourWEFerieVoyage' => ['N1' => 180, 'N2' => 180],
        ],
    ];

    $heuresEntries = $em->getRepository(HeuresEntry::class)->findBy([
        'osi' => $osi,
        'ouvrier' => $ouvrier,
    ]);

    $holidays = [];

    $getDayType = function(\DateTimeInterface $date, bool $workedOrStandbyOrVoyage) use ($holidays) {
        $dayOfWeek = (int)$date->format('N');
        $isWeekend = ($dayOfWeek >= 6);
        $isHoliday = in_array($date->format('Y-m-d'), $holidays);

        if (!$isWeekend && !$isHoliday) {
            return 'JourSemaine';
        }

        if (!$workedOrStandbyOrVoyage) {
            return 'JourWENonTravaille';
        }

        return 'JourWEFerieTravail';
    };

    $weeklyHours = [];
    $weeklyStandby = [];
    $weeklyPrimes = [];

    foreach ($weeks as $week) {
        $weekStart = (clone $week['start'])->setTime(0, 0, 0);
        $weekEnd = (clone $week['end'])->setTime(23, 59, 59);
        $sumWorkTravel = 0.0;
        $sumStandby = 0.0;
        $sumPrimes = 0.0;

        $dailyHours = [];

        foreach ($heuresEntries as $heuresEntry) {
            foreach ($heuresEntry->getDetails() as $detail) {
                $day = (clone $detail->getDay())->setTime(12, 0, 0);
                if ($day >= $weekStart && $day <= $weekEnd) {
                    $type = $detail->getType();
                    $hours = (float)($detail->getHours() ?? 0);
                    $category = $detail->getCategory();

                    $dayKey = $day->format('Y-m-d');

                    if (!isset($dailyHours[$dayKey])) {
                        $dailyHours[$dayKey] = [];
                    }
                    if (!isset($dailyHours[$dayKey][$type])) {
                        $dailyHours[$dayKey][$type] = [];
                    }
                    if (!isset($dailyHours[$dayKey][$type][$category])) {
                        $dailyHours[$dayKey][$type][$category] = 0;
                    }

                    $dailyHours[$dayKey][$type][$category] += $hours;

                    $typeLower = strtolower(trim($type));
                    if (in_array($typeLower, ['travail', 'voyage'])) {
                        $sumWorkTravel += $hours;
                    } elseif ($typeLower === 'stand-by') {
                        $sumStandby += $hours;
                    }
                }
            }
        }

        foreach ($dailyHours as $dayStr => $types) {
            $dateObj = new \DateTime($dayStr);

            foreach (['Travail', 'Voyage', 'Stand-By'] as $type) {
                if (!isset($types[$type])) {
                    continue;
                }
                foreach ($types[$type] as $niveau => $hours) {
                    if ($hours <= 0) continue;

                    $workedOrStandbyOrVoyage = true;

                    $dayTypeKey = $getDayType($dateObj, $workedOrStandbyOrVoyage);

                    if ($type === 'Voyage') {
                        $dayOfWeek = (int)$dateObj->format('N');
                        $isWeekend = ($dayOfWeek >= 6);
                        $isHoliday = in_array($dayStr, $holidays);
                        if ($isWeekend || $isHoliday) {
                            $dayTypeKey = 'JourWEFerieVoyage';
                        }
                    }

                    $niveauKey = strtoupper($niveau);
                    $rate = $rates[$zone][$dayTypeKey][$niveauKey] ?? 0;

                    $sumPrimes += $rate;
                }
            }
        }

        $weeklyHours[$weekStart->format('Y-m-d')] = $sumWorkTravel;
        $weeklyStandby[$weekStart->format('Y-m-d')] = $sumStandby;
        $weeklyPrimes[$weekStart->format('Y-m-d')] = $sumPrimes;
    }

    return $this->render('ouvriers/detail.html.twig', [
        'ouvrier' => $ouvrier,
        'osi' => $osi,
        'weeks' => $weeks,
        'weeklyHours' => $weeklyHours,
        'weeklyStandby' => $weeklyStandby,
        'weeklyPrimes' => $weeklyPrimes,
        'contratHoraire' => $contract->getContratHoraire(),
    ]);
}

    #[Route('/ouvrier/{prenomnom}/{numeroOSI}/approuver', name: 'app_ouvrier_approuver')]
public function approuver(string $prenomnom, int $numeroOSI, EntityManagerInterface $em): Response
{
    $osi = $em->getRepository(OSI::class)->findOneBy(['NumeroOSI' => $numeroOSI]);
    if (!$osi) {
        throw $this->createNotFoundException('OSI not found');
    }

    $ouvrier = null;
    foreach ($osi->getListeOuvriers() as $o) {
        $generated = strtolower(substr($o->getPrenom(), 0, 1) . $o->getNom());
        if ($generated === strtolower($prenomnom)) {
            $ouvrier = $o;
            break;
        }
    }

    if (!$ouvrier) {
        throw $this->createNotFoundException('Ouvrier not found in this OSI');
    }

    $country = $osi->getPays();
    $eurozone = [
        'France', 'Allemagne', 'Germany', 'Italie', 'Italy', 'Espagne', 'Spain',
        'Belgique', 'Belgium', 'Pays-Bas', 'Netherlands', 'Autriche', 'Austria',
        'Finlande', 'Finland', 'Irlande', 'Ireland', 'Grèce', 'Greece',
        'Slovaquie', 'Slovakia', 'Slovénie', 'Slovenia', 'Estonie', 'Estonia',
        'Lettonie', 'Latvia', 'Lituanie', 'Lithuania', 'Luxembourg',
        'Malte', 'Malta', 'Chypre', 'Cyprus'
    ];
    $zone = in_array(ucfirst(strtolower($country)), $eurozone) ? 'Zone Euros' : 'Hors Zone Euros';

    $rates = [
        'Zone Euros' => [
            'JourSemaine'        => ['N1'=>130,'N2'=>230],
            'JourWEFerieTravail' => ['N1'=>160,'N2'=>335],
            'JourWENonTravaille' => ['N1'=>90,'N2'=>180],
            'JourWEFerieVoyage'  => ['N1'=>160,'N2'=>160],
        ],
        'Hors Zone Euros' => [
            'JourSemaine'        => ['N1'=>150,'N2'=>250],
            'JourWEFerieTravail' => ['N1'=>180,'N2'=>355],
            'JourWENonTravaille' => ['N1'=>110,'N2'=>200],
            'JourWEFerieVoyage'  => ['N1'=>180,'N2'=>180],
        ],
    ];

    $holidays = []; // Optionally add holiday dates here

    $getDayType = function(\DateTimeInterface $date, bool $worked) use ($holidays) {
        $d = (int)$date->format('N');
        $we = $d >= 6;
        $ho = in_array($date->format('Y-m-d'), $holidays);

        if (!$we && !$ho) return 'JourSemaine';
        if (!$worked) return 'JourWENonTravaille';
        return 'JourWEFerieTravail';
    };

    $heuresEntries = $em->getRepository(HeuresEntry::class)
        ->findBy(['osi' => $osi, 'ouvrier' => $ouvrier]);

    $totalPrimesN1 = 0.0;
    $totalPrimesN2 = 0.0;
    $totalWork = 0.0;
    $totalTravel = 0.0;
    $totalStandby = 0.0;

    foreach ($heuresEntries as $entry) {
        foreach ($entry->getDetails() as $d) {
            $h = (float)($d->getHours() ?? 0);
            if ($h <= 0) continue;

            $type = strtolower($d->getType());
            $cat = strtoupper($d->getCategory());
            $date = $d->getDay();

            if ($type === 'voyage') $totalTravel += $h;
            if ($type === 'travail') $totalWork += $h;
            if ($type === 'stand-by') $totalStandby += $h;

            if (!in_array($type, ['travail', 'voyage', 'stand-by'])) continue;

            $dayType = $type === 'voyage' &&
                       ((int)$date->format('N') >= 6 || in_array($date->format('Y-m-d'), $holidays))
                ? 'JourWEFerieVoyage'
                : $getDayType($date, true);

            $rate = $rates[$zone][$dayType][$cat] ?? 0;

            if ($cat === 'N1') $totalPrimesN1 += $rate;
            if ($cat === 'N2') $totalPrimesN2 += $rate;
        }
    }

    // Heures Sup estimation
    $start = clone $osi->getDateDebut();
    $end = clone $osi->getDateFin();
    $nbWeeks = max(1, ceil($start->diff($end)->days / 7));

    $contrat = $em->getRepository(OuvrierOSIContrat::class)->findOneBy([
        'osi' => $osi,
        'ouvrier' => $ouvrier,
    ]);
    $contratHoraire = $contrat?->getContratHoraire() ?? 0;

    $loggedTotal = $totalWork + $totalTravel + $totalStandby;
    $rawOvertime = $loggedTotal - ($contratHoraire * $nbWeeks);
    $estimatedOvertime = max(0, $rawOvertime);

    return $this->render('ouvriers/approuver.html.twig', [
        'ouvrier' => $ouvrier,
        'osi' => $osi,
        'totalPrimesN1' => round($totalPrimesN1, 2),
        'totalPrimesN2' => round($totalPrimesN2, 2),
        'totalHours' => round($totalWork + $totalTravel, 2),
        'totalTravel' => round($totalTravel, 2),
        'totalActualHours' => round($loggedTotal, 2),    // Added actual hours here
        'estimatedOvertime' => round($estimatedOvertime, 2),
    ]);
}

#[Route('/ouvrier/{prenomnom}/{numeroOSI}/approuver/pdf', name: 'app_ouvrier_approuver_pdf')]
public function approuverPdf(string $prenomnom, int $numeroOSI, EntityManagerInterface $em): Response
{
    $osi = $em->getRepository(OSI::class)->findOneBy(['NumeroOSI' => $numeroOSI]);
    if (!$osi) {
        throw $this->createNotFoundException('OSI not found');
    }

    $ouvrier = null;
    foreach ($osi->getListeOuvriers() as $o) {
        $generated = strtolower(substr($o->getPrenom(), 0, 1) . $o->getNom());
        if ($generated === strtolower($prenomnom)) {
            $ouvrier = $o;
            break;
        }
    }

    if (!$ouvrier) {
        throw $this->createNotFoundException('Ouvrier not found in this OSI');
    }

    $country = $osi->getPays();
    $eurozone = [
        'France', 'Allemagne', 'Germany', 'Italie', 'Italy', 'Espagne', 'Spain',
        'Belgique', 'Belgium', 'Pays-Bas', 'Netherlands', 'Autriche', 'Austria',
        'Finlande', 'Finland', 'Irlande', 'Ireland', 'Grèce', 'Greece',
        'Slovaquie', 'Slovakia', 'Slovénie', 'Slovenia', 'Estonie', 'Estonia',
        'Lettonie', 'Latvia', 'Lituanie', 'Lithuania', 'Luxembourg',
        'Malte', 'Malta', 'Chypre', 'Cyprus'
    ];
    $zone = in_array(ucfirst(strtolower($country)), $eurozone) ? 'Zone Euros' : 'Hors Zone Euros';

    $rates = [
        'Zone Euros' => [
            'JourSemaine'        => ['N1'=>130,'N2'=>230],
            'JourWEFerieTravail' => ['N1'=>160,'N2'=>335],
            'JourWENonTravaille' => ['N1'=>90,'N2'=>180],
            'JourWEFerieVoyage'  => ['N1'=>160,'N2'=>160],
        ],
        'Hors Zone Euros' => [
            'JourSemaine'        => ['N1'=>150,'N2'=>250],
            'JourWEFerieTravail' => ['N1'=>180,'N2'=>355],
            'JourWENonTravaille' => ['N1'=>110,'N2'=>200],
            'JourWEFerieVoyage'  => ['N1'=>180,'N2'=>180],
        ],
    ];

    $holidays = []; // Add dates if needed

    $getDayType = function(\DateTimeInterface $date, bool $worked) use ($holidays) {
        $d = (int)$date->format('N');
        $we = $d >= 6;
        $ho = in_array($date->format('Y-m-d'), $holidays);

        if (!$we && !$ho) return 'JourSemaine';
        if (!$worked) return 'JourWENonTravaille';
        return 'JourWEFerieTravail';
    };

    $heuresEntries = $em->getRepository(HeuresEntry::class)
        ->findBy(['osi' => $osi, 'ouvrier' => $ouvrier]);

    $totalPrimesN1 = 0.0;
    $totalPrimesN2 = 0.0;
    $totalWork = 0.0;
    $totalTravel = 0.0;
    $totalStandby = 0.0;

    foreach ($heuresEntries as $entry) {
        foreach ($entry->getDetails() as $d) {
            $h = (float)($d->getHours() ?? 0);
            if ($h <= 0) continue;

            $type = strtolower($d->getType());
            $cat = strtoupper($d->getCategory());
            $date = $d->getDay();

            if ($type === 'voyage') $totalTravel += $h;
            if ($type === 'travail') $totalWork += $h;
            if ($type === 'stand-by') $totalStandby += $h;

            if (!in_array($type, ['travail', 'voyage', 'stand-by'])) continue;

            $dayType = $type === 'voyage' &&
                       ((int)$date->format('N') >= 6 || in_array($date->format('Y-m-d'), $holidays))
                ? 'JourWEFerieVoyage'
                : $getDayType($date, true);

            $rate = $rates[$zone][$dayType][$cat] ?? 0;

            if ($cat === 'N1') $totalPrimesN1 += $rate;
            if ($cat === 'N2') $totalPrimesN2 += $rate;
        }
    }

    $totalHours = $totalWork + $totalTravel;
    $totalActualHours = $totalWork + $totalTravel + $totalStandby;

    $start = clone $osi->getDateDebut();
    $end = clone $osi->getDateFin();
    $nbWeeks = max(1, ceil($start->diff($end)->days / 7));

    $contrat = $em->getRepository(OuvrierOSIContrat::class)->findOneBy([
        'osi' => $osi,
        'ouvrier' => $ouvrier,
    ]);
    $contratHoraire = $contrat?->getContratHoraire() ?? 0;

    $loggedTotal = $totalActualHours;
    $rawOvertime = $loggedTotal - ($contratHoraire * $nbWeeks);
    $estimatedOvertime = max(0, $rawOvertime);

    $html = $this->renderView('ouvriers/approuver_pdf.html.twig', [
        'ouvrier' => $ouvrier,
        'osi' => $osi,
        'totalPrimesN1' => round($totalPrimesN1, 2),
        'totalPrimesN2' => round($totalPrimesN2, 2),
        'totalHours' => round($totalHours, 2),
        'totalTravel' => round($totalTravel, 2),
        'totalActualHours' => round($totalActualHours, 2),
        'estimatedOvertime' => round($estimatedOvertime, 2),
    ]);

    $pdf = new \Dompdf\Dompdf();
    $pdf->loadHtml($html);
    $pdf->setPaper('A4', 'portrait');
    $pdf->render();

    $filename = sprintf(
        'Indemnisation Individuelle - OSI %s - %s %s.pdf',
        $osi->getNumeroOSI(),
        strtoupper($ouvrier->getNom()),
        ucfirst(strtolower($ouvrier->getPrenom()))
    );

    // Sanitize filename
    $filename = preg_replace('/[^\w\s.-]/u', '', $filename);

    return new Response(
        $pdf->output(),
        200,
        [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
        ]
    );
}





}
