<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use App\Entity\OSI;
use App\Entity\InfoOuvrier;

#[ORM\Entity]
class HeuresEntry
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type:"integer")]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: OSI::class)]
    #[ORM\JoinColumn(nullable:false)]
    private ?OSI $osi = null;

    #[ORM\ManyToOne(targetEntity: InfoOuvrier::class)]
    #[ORM\JoinColumn(nullable:false)]
    private ?InfoOuvrier $ouvrier = null;

    #[ORM\Column(type:"date")]
    private ?\DateTimeInterface $startWeekDate = null;

    #[ORM\OneToMany(mappedBy: "heuresEntry", targetEntity: HeuresDetail::class, cascade:["persist", "remove"], orphanRemoval:true)]
    private Collection $details;

    public function __construct()
    {
        $this->details = new ArrayCollection();
    }

    // getters/setters...

    public function getId(): ?int { return $this->id; }

    public function getOsi(): ?OSI { return $this->osi; }
    public function setOsi(?OSI $osi): static { $this->osi = $osi; return $this; }

    public function getOuvrier(): ?InfoOuvrier { return $this->ouvrier; }
    public function setOuvrier(?InfoOuvrier $ouvrier): static { $this->ouvrier = $ouvrier; return $this; }

    public function getStartWeekDate(): ?\DateTimeInterface { return $this->startWeekDate; }
    public function setStartWeekDate(\DateTimeInterface $date): static { $this->startWeekDate = $date; return $this; }

    /**
     * @return Collection|HeuresDetail[]
     */
    public function getDetails(): Collection { return $this->details; }

    public function addDetail(HeuresDetail $detail): static
    {
        if (!$this->details->contains($detail)) {
            $this->details[] = $detail;
            $detail->setHeuresEntry($this);
        }
        return $this;
    }

    public function removeDetail(HeuresDetail $detail): static
    {
        if ($this->details->removeElement($detail)) {
            if ($detail->getHeuresEntry() === $this) {
                $detail->setHeuresEntry(null);
            }
        }
        return $this;
    }
}
