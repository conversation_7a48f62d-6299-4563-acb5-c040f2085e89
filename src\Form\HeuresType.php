<?php

namespace App\Form;

use App\Entity\Heures;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class HeuresType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('workingN1', NumberType::class, ['label' => 'Working N1 (heures)', 'required' => false])
            ->add('standbyN1', NumberType::class, ['label' => 'Standby N1 (heures)', 'required' => false])
            ->add('travelN1', NumberType::class, ['label' => 'Travel N1 (heures)', 'required' => false])
            ->add('workingN2', NumberType::class, ['label' => 'Working N2 (heures)', 'required' => false])
            ->add('standbyN2', NumberType::class, ['label' => 'Standby N2 (heures)', 'required' => false])
            ->add('travelN2', NumberType::class, ['label' => 'Travel N2 (heures)', 'required' => false])
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Heures::class,
        ]);
    }
}
