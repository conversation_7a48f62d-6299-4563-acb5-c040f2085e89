{# templates/osi/list.html.twig #}
{% extends 'base.html.twig' %}

{% block title %}Liste OSI{% endblock %}

{% block body %}
  <h1>Liste des OSI</h1>

  {# Search by Ouvrier #}
  <form method="get" action="{{ path('app_osi_list') }}">
    <label for="ouvrier">Rechercher par ouvrier :</label>
    <input type="text" name="ouvrier" id="ouvrier" value="{{ searchTermOuvrier|default('') }}">
    <button type="submit">Rechercher</button>
  </form>

  {# Search by Client #}
  <form method="get" action="{{ path('app_osi_list') }}">
    <label for="client">Rechercher par client :</label>
    <input type="text" name="client" id="client" value="{{ searchTermClient|default('') }}">
    <button type="submit">Rechercher</button>
  </form>

  <table border="1" cellspacing="0" cellpadding="5">
    <thead>
      <tr>
        <th>Numéro OSI</th>
        <th>Client</th>
        <th>Projet</th>
        <th>Pays</th>
        <th>Ville</th>
        <th>Site</th>
        <th>Dates</th>
        <th>Ouvriers</th>
        <th>Numero d'Achat</th>
        <th>Facturable ?</th>
        <th><a href="{{ path('app_osi') }}">Nouvel OSI</a></th>
      </tr>
    </thead>
    <tbody>
      {% for osi in osis %}
        <tr>
          <td>{{ osi.NumeroOSI }}</td>
          <td>{{ osi.NomClient }}</td>
          <td>{{ osi.NomProjet }}</td>
          <td>{{ osi.Pays }}</td>
          <td>{{ osi.Ville }}</td>
          <td>{{ osi.Site }}</td>
          <td>du {{ osi.DateDebut ? osi.DateDebut|date('d/m/Y') : 'N/A' }} au {{ osi.DateFin ? osi.DateFin|date('d/m/Y') : 'N/A' }}</td>
          <td>
            {# Use sorted ouvriers if available, fallback to original #}
            {% set ouvriers = sortedOuvriersByOSI[osi.NumeroOSI]|default(osi.ListeOuvriers) %}
            {% for ouvrier in ouvriers %}
              {{ ouvrier.nom }} {{ ouvrier.prenom }}{% if not loop.last %} | {% endif %}
            {% endfor %}
          </td>
          <td>{{ osi.NumeroAchat }}</td>
          <td>{{ osi.Billable ? 'Oui' : 'Non' }}</td>
          <td>
            <a href="{{ path('app_osi_detail', {'numeroOSI': osi.NumeroOSI}) }}">Configurer</a>
          </td>
        </tr>
      {% else %}
        <tr><td colspan="12">Aucun résultat trouvé</td></tr>
      {% endfor %}
    </tbody>
  </table>
{% endblock %}
