{% extends 'base.html.twig' %}

{% block title %}Détails OSI - {{ osi.NumeroOSI }}{% endblock %}

{% block body %}
  <h1>Détails OSI - {{ osi.NumeroOSI }}</h1>
  <p>du {{ osi.DateDebut ? osi.DateDebut|date('d/m/Y') : 'N/A' }} au {{ osi.DateFin ? osi.DateFin|date('d/m/Y') : 'N/A' }}</p>

  <table border="1" cellspacing="0" cellpadding="5">
  <thead>
    <tr>
      <th>Ouvrier</th>
      <th>Heures Travaillées / Voyagées</th>
      <th>Heures Stand-By</th>
      <th>Total Primes</th>
      <th></th>
    </tr>
  </thead>
  <tbody>
    {% for ouvrier in sortedOuvriers %}
      <tr>
        <td>{{ ouvrier.nom }} {{ ouvrier.prenom }}</td>
        <td>{{ workedHoursPerOuvrier[ouvrier.id] ?? 0 }}</td>
        <td>{{ standbyHoursPerOuvrier[ouvrier.id] ?? 0 }}</td>
        <td>{{ totalPrimesPerOuvrier[ouvrier.id]|default(0)|number_format(2, ',', ' ') }}</td>
        <td>
          <a href="{{ path('app_ouvrier_detail', {
            'prenomnom': (ouvrier.prenom|lower|slice(0,1)) ~ ouvrier.nom|lower,
            'numeroOSI': osi.NumeroOSI
          }) }}">
            Configurer
          </a>
        </td>
      </tr>
    {% else %}
      <tr>
        <td colspan="5">Aucun ouvrier associé à cette OSI.</td>
      </tr>
    {% endfor %}
  </tbody>
</table>

  <p><a href="{{ path('app_osi_list') }}">← Retour à la liste OSI</a></p>
{% endblock %}
