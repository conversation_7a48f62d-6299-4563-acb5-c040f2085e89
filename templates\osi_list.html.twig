{% if pagination %}
    <nav aria-label="Page navigation example">
        <ul class="pagination">
            {% if pagination.currentPageNumber > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ path('app_osi_list', app.request.query.all|merge({'page': pagination.currentPageNumber - 1})) }}">Précédent</a>
                </li>
            {% endif %}

            {% for page in 1..pagination.pageCount %}
                <li class="page-item {% if page == pagination.currentPageNumber %}active{% endif %}">
                    <a class="page-link" href="{{ path('app_osi_list', app.request.query.all|merge({'page': page})) }}">{{ page }}</a>
                </li>
            {% endfor %}

            {% if pagination.currentPageNumber < pagination.pageCount %}
                <li class="page-item">
                    <a class="page-link" href="{{ path('app_osi_list', app.request.query.all|merge({'page': pagination.currentPageNumber + 1})) }}">Suivant</a>
                </li>
            {% endif %}
        </ul>
    </nav>
