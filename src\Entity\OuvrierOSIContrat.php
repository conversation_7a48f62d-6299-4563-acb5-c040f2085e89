<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Repository\OuvrierOSIContratRepository;

#[ORM\Entity(repositoryClass: OuvrierOSIContratRepository::class)]
class OuvrierOSIContrat
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: OSI::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?OSI $osi = null;

    #[ORM\ManyToOne(targetEntity: InfoOuvrier::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?InfoOuvrier $ouvrier = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $contratHoraire = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOsi(): ?OSI
    {
        return $this->osi;
    }

    public function setOsi(?OSI $osi): static
    {
        $this->osi = $osi;
        return $this;
    }

    public function getOuvrier(): ?InfoOuvrier
    {
        return $this->ouvrier;
    }

    public function setOuvrier(?InfoOuvrier $ouvrier): static
    {
        $this->ouvrier = $ouvrier;
        return $this;
    }

    public function getContratHoraire(): ?float
    {
        return $this->contratHoraire;
    }

    public function setContratHoraire(?float $contratHoraire): static
    {
        $this->contratHoraire = $contratHoraire;
        return $this;
    }
}
