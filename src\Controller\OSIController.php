<?php

namespace App\Controller;

use App\Form\OSIForm;
use App\Entity\OSI;
use App\Entity\InfoOuvrier;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class OSIController extends AbstractController
{
    #[Route('/osi', name: 'app_osi')]
    public function osi(Request $request, EntityManagerInterface $entityManager): Response
    {
        $osi = new OSI();
        $form = $this->createForm(OSIForm::class, $osi);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($osi);
            $entityManager->flush();

            return $this->redirectToRoute('app_osi_list');
        }

        return $this->render('osi.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/osi/list', name: 'app_osi_list')]
    public function list(Request $request, EntityManagerInterface $entityManager): Response
    {
        $searchOuvrier = $request->query->get('ouvrier');
        $searchClient = $request->query->get('client');

        if ($searchOuvrier) {
            // Search by ouvrier only
            $osis = $entityManager->createQuery(
                'SELECT o
                 FROM App\Entity\OSI o
                 JOIN o.ListeOuvriers w
                 WHERE LOWER(w.nom) LIKE :term OR LOWER(w.prenom) LIKE :term
                 ORDER BY o.NumeroOSI ASC'
            )
            ->setParameter('term', '%' . strtolower($searchOuvrier) . '%')
            ->getResult();
        } elseif ($searchClient) {
            // Search by client only
            $osis = $entityManager->createQuery(
                'SELECT o
                 FROM App\Entity\OSI o
                 WHERE LOWER(o.NomClient) LIKE :term
                 ORDER BY o.NumeroOSI ASC'
            )
            ->setParameter('term', '%' . strtolower($searchClient) . '%')
            ->getResult();
        } else {
            // No search — list all
            $osis = $entityManager->getRepository(OSI::class)->findBy([], ['NumeroOSI' => 'ASC']);
        }

        // Prepare an array of sorted ouvriers per OSI id
        $sortedOuvriersByOSI = [];

        foreach ($osis as $osi) {
            $ouvriers = $osi->getListeOuvriers()->toArray();
            usort($ouvriers, function ($a, $b) {
                return strcmp($a->getNom(), $b->getNom());
            });
            $sortedOuvriersByOSI[$osi->getNumeroOSI()] = $ouvriers;
        }

        return $this->render('osi/list.html.twig', [
            'osis' => $osis,
            'searchTermOuvrier' => $searchOuvrier,
            'searchTermClient' => $searchClient,
            'sortedOuvriersByOSI' => $sortedOuvriersByOSI, // Pass sorted ouvriers here
        ]);
    }

    #[Route('/osi/list/{numeroOSI}', name: 'app_osi_detail')]
public function detail(int $numeroOSI, EntityManagerInterface $entityManager): Response
{
    $osi = $entityManager->getRepository(OSI::class)->findOneBy(['NumeroOSI' => $numeroOSI]);

    if (!$osi) {
        throw $this->createNotFoundException('OSI not found');
    }

    // Sort ouvriers by nom ascending
    $ouvriers = $osi->getListeOuvriers()->toArray();
    usort($ouvriers, function($a, $b) {
        return strcmp(mb_strtolower($a->getNom()), mb_strtolower($b->getNom()));
    });
    // Replace the collection with sorted array if needed (or pass sorted array to Twig)
    // We’ll pass sorted ouvriers as a variable to Twig
    // (Do NOT try to setListeOuvriers if this method doesn't exist)

    $startDate = $osi->getDateDebut();
    $endDate = $osi->getDateFin();

    $workedHoursPerOuvrier = [];
    $standbyHoursPerOuvrier = [];
    $totalPrimesPerOuvrier = [];

    $eurozoneCountries = [
        'France', 'Germany', 'Allemagne', 'Italy', 'Italie', 'Spain', 'Espagne', 'Portugal', 'Belgique', 'Belgium',
        'Netherlands', 'Pays-Bas', 'Austria', 'Autriche', 'Finland', 'Finlande', 'Ireland', 'Irlande', 'Greece', 'Grèce',
        'Slovakia', 'Slovaquie', 'Slovenia', 'Slovénie', 'Estonia', 'Estonie', 'Latvia', 'Lettonie', 'Lithuania', 'Lituanie',
        'Luxembourg', 'Malta', 'Malte', 'Cyprus', 'Chypre',
    ];

    $rates = [
        'Zone Euros' => [
            'JourSemaine' => ['N1' => 130, 'N2' => 230],
            'JourWEFerieTravail' => ['N1' => 160, 'N2' => 335],
            'JourWENonTravaille' => ['N1' => 90, 'N2' => 180],
            'JourWEFerieVoyage' => ['N1' => 160, 'N2' => 160],
        ],
        'Hors Zone Euros' => [
            'JourSemaine' => ['N1' => 150, 'N2' => 250],
            'JourWEFerieTravail' => ['N1' => 180, 'N2' => 355],
            'JourWENonTravaille' => ['N1' => 110, 'N2' => 200],
            'JourWEFerieVoyage' => ['N1' => 180, 'N2' => 180],
        ],
    ];

    $holidays = []; // Add holiday dates here if needed

    $getDayType = function(\DateTimeInterface $date, bool $workedOrStandbyOrVoyage) use ($holidays) {
        $dayOfWeek = (int)$date->format('N');
        $isWeekend = ($dayOfWeek >= 6);
        $isHoliday = in_array($date->format('Y-m-d'), $holidays);

        if (!$isWeekend && !$isHoliday) {
            return 'JourSemaine';
        }

        return $workedOrStandbyOrVoyage ? 'JourWEFerieTravail' : 'JourWENonTravaille';
    };

    foreach ($ouvriers as $ouvrier) {
        $workedHours = 0.0;
        $standbyHours = 0.0;
        $totalPrime = 0.0;

        $heuresEntries = $entityManager->getRepository('App\Entity\HeuresEntry')->findBy([
            'osi' => $osi,
            'ouvrier' => $ouvrier,
        ]);

        $zone = in_array(ucfirst(strtolower($osi->getPays())), $eurozoneCountries) ? 'Zone Euros' : 'Hors Zone Euros';

        foreach ($heuresEntries as $entry) {
            foreach ($entry->getDetails() as $detail) {
                $day = $detail->getDay();
                if ($day < $startDate || $day > $endDate) continue;

                $type = $detail->getType();
                $category = $detail->getCategory();
                $hours = (float) ($detail->getHours() ?? 0);
                if ($hours <= 0) continue;

                $typeLower = strtolower($type);
                if (in_array($typeLower, ['travail', 'voyage'])) {
                    $workedHours += $hours;
                } elseif ($typeLower === 'stand-by') {
                    $standbyHours += $hours;
                }

                $workedOrStandbyOrVoyage = true;
                $dayType = $getDayType($day, $workedOrStandbyOrVoyage);

                if ($typeLower === 'voyage') {
                    $dow = (int)$day->format('N');
                    $isHoliday = in_array($day->format('Y-m-d'), $holidays);
                    if ($dow >= 6 || $isHoliday) {
                        $dayType = 'JourWEFerieVoyage';
                    }
                }

                $niveau = strtoupper(trim($category));
                $rate = $rates[$zone][$dayType][$niveau] ?? 0;

                $totalPrime += $rate;
            }
        }

        $workedHoursPerOuvrier[$ouvrier->getId()] = $workedHours;
        $standbyHoursPerOuvrier[$ouvrier->getId()] = $standbyHours;
        $totalPrimesPerOuvrier[$ouvrier->getId()] = $totalPrime;
    }

    return $this->render('osi/detail.html.twig', [
        'osi' => $osi,
        'workedHoursPerOuvrier' => $workedHoursPerOuvrier,
        'standbyHoursPerOuvrier' => $standbyHoursPerOuvrier,
        'totalPrimesPerOuvrier' => $totalPrimesPerOuvrier,
        'sortedOuvriers' => $ouvriers,  // Pass sorted ouvriers to Twig
    ]);
}

}
