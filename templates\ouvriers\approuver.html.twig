{% extends 'base.html.twig' %}
{% block title %}INDEMNISATION INDIVIDUELLE OSI{% endblock %}

{% block body %}
  <h1>INDEMNISATION INDIVIDUELLE OSI</h1>

  <table cellpadding="5" cellspacing="0">
    <tr>
      <td><strong>NOM Prénom :</strong></td>
      <td>{{ ouvrier.nom|upper }} {{ ouvrier.prenom }}</td>
      <td><strong>Date de Départ :</strong></td>
      <td>{{ osi.DateDebut|date('d/m/Y') }}</td>
    </tr>
    <tr>
      <td><strong>Projet :</strong></td>
      <td>{{ osi.nomProjet|default('N/A') }}</td>
      <td><strong>Date de Retour :</strong></td>
      <td>{{ osi.DateFin|date('d/m/Y') }}</td>
    </tr>
    <tr>
      <td><strong>OSI :</strong></td>
      <td>{{ osi.NumeroOSI }}</td>
      <td><strong>Lieu de travail :</strong></td>
      <td>{{ osi.pays }}, {{ osi.ville|default('') }}, {{ osi.site|default('') }}</td>
    </tr>
  </table>

  <br>

  <table border="1" cellpadding="5" cellspacing="0">
    <thead>
      <tr><th colspan="5">RÉCAPITULATIF</th></tr>
    </thead>
    <tbody>
      <tr>
        <td colspan="2"><strong>TOTAL PRIMES</strong></td>
        <td colspan="1">N1 : {{ totalPrimesN1|number_format(2, ',', ' ') }} €</td>
        <td colspan="1">N2 : {{ totalPrimesN2|number_format(2, ',', ' ') }} €</td>
        <td colspan="1">Total : {{ (totalPrimesN1 + totalPrimesN2)|number_format(2, ',', ' ') }} €</td>
      </tr>
      <tr>
        <td colspan="2"><strong>TOTAL HEURES TRAVAILLÉES / VOYAGÉES</strong></td>
        <td colspan="1">{{ totalHours }}h</td>
        <td colspan="2">dont {{ totalTravel }}h de voyage</td>
      </tr>
      <tr>
        <td colspan="2"><strong>ESTIMATION HEURES SUPPLÉMENTAIRES</strong>
          <h5>ATTENTION: ce total sera revu par le service RH <br>avec les heures réalisées sur le site du Mans</h5>
        </td>
        <td colspan="3">
          {% if estimatedOvertime is defined %}
            {{ estimatedOvertime|number_format(2, ',', ' ') }} h
          {% else %}
            - à définir
          {% endif %}
        </td>
      </tr>
    </tbody>
  </table>

  <p>
    <a href="#">[Approuver]</a>          <a href="##">[Approuver]</a>          <a href="###">[Approuver]</a>          <a href="####">[Approuver]</a>          <a href="#####">[Approuver]</a>
  </p>

  <p>
    <a href="{{ path('app_ouvrier_detail', {
       'prenomnom': ouvrier.prenom|lower|slice(0,1) ~ ouvrier.nom|lower,
       'numeroOSI': osi.NumeroOSI
    }) }}">← Retour à l'ouvrier</a>                  <a href="{{ path('app_ouvrier_approuver_pdf', {
   'prenomnom': ouvrier.prenom|lower|slice(0,1) ~ ouvrier.nom|lower,
   'numeroOSI': osi.NumeroOSI
}) }}" target="_blank">[Exporter en tant que .pdf →]</a>

  </p>
{% endblock %}
