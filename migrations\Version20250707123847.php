<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250707123847 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD osi_id INT NOT NULL, ADD ouvrier_id INT NOT NULL, ADD week_start DATE NOT NULL, ADD standby_n1 DOUBLE PRECISION DEFAULT '0' NOT NULL, ADD standby_n2 DOUBLE PRECISION DEFAULT '0' NOT NULL, ADD travel_n1 DOUBLE PRECISION DEFAULT '0' NOT NULL, ADD travel_n2 DOUBLE PRECISION DEFAULT '0' NOT NULL, DROP voyage_n1, DROP voyage_n2, DROP stand_by_n1, DROP stand_by_n2, CHANGE working_n1 working_n1 DOUBLE PRECISION DEFAULT '0' NOT NULL, CHANGE working_n2 working_n2 DOUBLE PRECISION DEFAULT '0' NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD CONSTRAINT FK_DEA5875D293C2298 FOREIGN KEY (osi_id) REFERENCES osi (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD CONSTRAINT FK_DEA5875D4E853A9E FOREIGN KEY (ouvrier_id) REFERENCES info_ouvrier (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DEA5875D293C2298 ON heures (osi_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_DEA5875D4E853A9E ON heures (ouvrier_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi CHANGE nom_client nom_client VARCHAR(255) NOT NULL, CHANGE nom_projet nom_projet VARCHAR(255) NOT NULL, CHANGE pays pays VARCHAR(255) NOT NULL, CHANGE ville ville VARCHAR(255) NOT NULL, CHANGE site site VARCHAR(255) NOT NULL, CHANGE numero_osi numero_osi INT NOT NULL, CHANGE numero_achat numero_achat DOUBLE PRECISION NOT NULL, CHANGE billable billable TINYINT(1) NOT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE heures DROP FOREIGN KEY FK_DEA5875D293C2298
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures DROP FOREIGN KEY FK_DEA5875D4E853A9E
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_DEA5875D293C2298 ON heures
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_DEA5875D4E853A9E ON heures
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE heures ADD voyage_n1 DOUBLE PRECISION NOT NULL, ADD voyage_n2 DOUBLE PRECISION NOT NULL, ADD stand_by_n1 DOUBLE PRECISION NOT NULL, ADD stand_by_n2 DOUBLE PRECISION NOT NULL, DROP osi_id, DROP ouvrier_id, DROP week_start, DROP standby_n1, DROP standby_n2, DROP travel_n1, DROP travel_n2, CHANGE working_n1 working_n1 DOUBLE PRECISION NOT NULL, CHANGE working_n2 working_n2 DOUBLE PRECISION NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE osi CHANGE nom_client nom_client VARCHAR(255) DEFAULT NULL, CHANGE nom_projet nom_projet VARCHAR(255) DEFAULT NULL, CHANGE pays pays VARCHAR(255) DEFAULT NULL, CHANGE ville ville VARCHAR(255) DEFAULT NULL, CHANGE site site VARCHAR(255) DEFAULT NULL, CHANGE numero_osi numero_osi INT UNSIGNED NOT NULL, CHANGE numero_achat numero_achat DOUBLE PRECISION UNSIGNED DEFAULT NULL, CHANGE billable billable TINYINT(1) DEFAULT NULL
        SQL);
    }
}
