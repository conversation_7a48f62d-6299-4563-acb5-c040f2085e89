<?php

namespace App\Form;

use App\Entity\HeuresDay;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class HeuresDayType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('date', DateType::class, [
                'widget' => 'single_text',
                'disabled' => true,
            ])
            ->add('workingN1', NumberType::class, ['scale' => 2])
            ->add('standbyN1', NumberType::class, ['scale' => 2])
            ->add('travelN1', NumberType::class, ['scale' => 2])
            ->add('workingN2', NumberType::class, ['scale' => 2])
            ->add('standbyN2', NumberType::class, ['scale' => 2])
            ->add('travelN2', NumberType::class, ['scale' => 2])
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => HeuresDay::class,
        ]);
    }
}
