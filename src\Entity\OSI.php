<?php

namespace App\Entity;

use App\Repository\OSIRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use App\Entity\InfoOuvrier;
use App\Entity\OuvrierOSIContrat;

#[ORM\Entity(repositoryClass: OSIRepository::class)]
class OSI
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $NomClient = null;

    #[ORM\Column(length: 255)]
    private ?string $NomProjet = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Pays = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Ville = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Site = null;

    #[ORM\Column]
    private ?int $NumeroOSI = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $NumeroAchat = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $DateDebut = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $DateFin = null;

    #[ORM\ManyToMany(targetEntity: InfoOuvrier::class)]
    private Collection $ListeOuvriers;

    #[ORM\OneToMany(mappedBy: "osi", targetEntity: OuvrierOSIContrat::class, cascade: ["persist", "remove"], orphanRemoval: true)]
    private Collection $ouvrierContrats;

    #[ORM\Column]
    private ?bool $Billable = null;

    public function __construct()
    {
        $this->ListeOuvriers = new ArrayCollection();
        $this->ouvrierContrats = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNomClient(): ?string
    {
        return $this->NomClient;
    }

    public function setNomClient(string $NomClient): static
    {
        $this->NomClient = $NomClient;

        return $this;
    }

    public function getNomProjet(): ?string
    {
        return $this->NomProjet;
    }

    public function setNomProjet(string $NomProjet): static
    {
        $this->NomProjet = $NomProjet;

        return $this;
    }

    public function getPays(): ?string
    {
        return $this->Pays;
    }

    public function setPays(?string $Pays): static
    {
        $this->Pays = $Pays;

        return $this;
    }

    public function getVille(): ?string
    {
        return $this->Ville;
    }

    public function setVille(?string $Ville): static
    {
        $this->Ville = $Ville;

        return $this;
    }

    public function getSite(): ?string
    {
        return $this->Site;
    }

    public function setSite(?string $Site): static
    {
        $this->Site = $Site;

        return $this;
    }

    public function getNumeroOSI(): ?int
    {
        return $this->NumeroOSI;
    }

    public function setNumeroOSI(int $NumeroOSI): static
    {
        $this->NumeroOSI = $NumeroOSI;

        return $this;
    }

    public function getNumeroAchat(): ?float
    {
        return $this->NumeroAchat;
    }

    public function setNumeroAchat(?float $NumeroAchat): static
    {
        $this->NumeroAchat = $NumeroAchat;

        return $this;
    }

    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->DateDebut;
    }

    public function setDateDebut(?\DateTimeInterface $DateDebut): static
    {
        $this->DateDebut = $DateDebut;

        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->DateFin;
    }

    public function setDateFin(?\DateTimeInterface $DateFin): static
    {
        $this->DateFin = $DateFin;

        return $this;
    }

    public function getListeOuvriers(): Collection
    {
        return $this->ListeOuvriers;
    }

    public function addListeOuvrier(InfoOuvrier $ouvrier): static
    {
        if (!$this->ListeOuvriers->contains($ouvrier)) {
            $this->ListeOuvriers->add($ouvrier);
        }

        return $this;
    }

    public function removeListeOuvrier(InfoOuvrier $ouvrier): static
    {
        $this->ListeOuvriers->removeElement($ouvrier);

        return $this;
    }

    public function getOuvrierContrats(): Collection
    {
        return $this->ouvrierContrats;
    }

    public function addOuvrierContrat(OuvrierOSIContrat $contrat): static
    {
        if (!$this->ouvrierContrats->contains($contrat)) {
            $this->ouvrierContrats[] = $contrat;
            $contrat->setOsi($this);
        }

        return $this;
    }

    public function removeOuvrierContrat(OuvrierOSIContrat $contrat): static
    {
        if ($this->ouvrierContrats->removeElement($contrat)) {
            if ($contrat->getOsi() === $this) {
                $contrat->setOsi(null);
            }
        }

        return $this;
    }

    public function isBillable(): ?bool
    {
        return $this->Billable;
    }

    public function setBillable(bool $Billable): static
    {
        $this->Billable = $Billable;

        return $this;
    }
}
