<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Calibri, sans-serif;
            font-size: 12px;
            margin: 40px;
        }
        .header-table {
            width: 100%;
            border: 1px solid black;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .header-table td {
            padding: 10px;
            vertical-align: middle;
        }
        .logo-cell {
            font-weight: bold;
            width: 150px; 
            border-right: 1px solid black;
        }
        .logo-cell img {
            max-height: 70px;
            width: auto;
        }
        .title-cell {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .confidential {
            font-size: 16px;
            color: red;
            font-weight: bold;
            text-align: center;
            margin-top: 30px;
            margin-bottom: 30px;
            padding: 10px 0;
        }
        .red {
            color: red;
        }
        .info-table {
            width: 100%;
            margin-bottom: 20px;
        }
        .info-table td {
            padding: 4px 8px;
            vertical-align: top;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .result-table th,
        .result-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: left;
        }
        .result-table th {
            background-color: #eee;
        }
        /* Make first two rows taller and emphasize text */
        .result-table tr:first-child td,
        .result-table tr:nth-child(2) td {
            padding-top: 16px;
            padding-bottom: 16px;
            font-weight: bold;
            font-size: 14px;
        }
        /* Special styling for the "voyage" cell in second row */
        .result-table tr:nth-child(2) td.voyage {
            font-style: italic;
            font-weight: normal;
            font-size: 12px;
        }
        .voyage {
            font-style: italic;
        }
        .attention {
            color: red;
            font-style: italic;
            margin-top: 10px;
        }
    </style>
</head>
<body>

    <table class="header-table">
        <tr>
            <td class="logo-cell">
                <img src="assets/images/SCMLogo.jpg" alt="SCM Logo">
            </td>
            <td class="title-cell">INDEMNISATION INDIVIDUELLE OSI</td>
        </tr>
    </table>

    <div class="confidential">DOCUMENT CONFIDENTIEL : NE PAS DIFFUSER</div>

    <table class="info-table">
        <tr>
            <td><strong><u>NOM Prénom</u> :</strong></td>
            <td>{{ ouvrier.nom|upper }} {{ ouvrier.prenom }}</td>
            <td><strong><u>Date de Départ</u> :</strong></td>
            <td>{{ osi.dateDebut ? osi.dateDebut|date('d/m/Y') : 'N/A' }}</td>
        </tr>
        <tr>
            <td><strong><u>Projet</u> :</strong></td>
            <td>{{ osi.nomProjet|default('N/A') }}</td>
            <td><strong><u>Date de Retour</u> :</strong></td>
            <td>{{ osi.dateFin ? osi.dateFin|date('d/m/Y') : 'N/A' }}</td>
        </tr>
        <tr>
            <td><strong><u>OSI</u> :</strong></td>
            <td>{{ osi.numeroOSI }}</td>
            <td><strong><u>Lieu de travail</u> :</strong></td>
            <td>{{ osi.pays }}</td>
        </tr>
    </table>

    <table class="result-table">
        <tr>
            <td class="red"><strong>TOTAL PRIMES</strong></td>
            <td class="voyage">N1 : {{ totalPrimesN1|number_format(2, ',', ' ') }} €</td>
            <td class="voyage">N2 : {{ totalPrimesN2|number_format(2, ',', ' ') }} €</td>
            <td class="red"><strong>Total : {{ (totalPrimesN1 + totalPrimesN2)|number_format(2, ',', ' ') }} €</strong></td>
        </tr>
        <tr>
            <td colspan="2" class="red"><strong>
                TOTAL HEURES TRAVAILLÉES / VOYAGÉES : {{ totalHours|number_format(2, ',', ' ') }} h
            </strong></td>
            <td colspan="2" class="voyage">
                dont heures de voyage : {{ totalTravel|number_format(2, ',', ' ') }} h
            </td>
        </tr>
        <tr>
            <td colspan="2" >
                <strong><u>ESTIMATION</u> HEURES SUPPLÉMENTAIRES</strong>
                <p class="attention">
                    <u>ATTENTION</u> : ce total sera revu par le service <br>RH avec les heures réalisées sur le site du Mans
                </p>
            </td>
            <td colspan="2">    
                {% if estimatedOvertime is defined %}
                    {{ estimatedOvertime|number_format(2, ',', ' ') }} h
                {% else %}
                    0,00 h
                {% endif %}
            </td>
        </tr>
    </table>

</body>
</html>
